#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
提取shapefile中最外部边界的脚本
读取basins_lev05.shp，提取最外部边界，保存为basins_lev05_boundary.shp
"""

import geopandas as gpd
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
import os

def extract_outer_boundary(input_shp, output_shp):
    """
    提取shapefile中所有几何体的最外部边界
    
    Parameters:
    -----------
    input_shp : str
        输入shapefile路径
    output_shp : str
        输出shapefile路径
    """
    
    print(f"正在读取文件: {input_shp}")
    
    # 读取shapefile
    gdf = gpd.read_file(input_shp)
    
    print(f"原始数据包含 {len(gdf)} 个要素")
    print(f"坐标系统: {gdf.crs}")
    
    # 合并所有几何体
    print("正在合并所有几何体...")
    merged_geometry = unary_union(gdf.geometry)
    
    # 提取最外部边界
    print("正在提取最外部边界...")
    if isinstance(merged_geometry, (Polygon, MultiPolygon)):
        # 获取外边界
        if isinstance(merged_geometry, Polygon):
            # 单个多边形，获取外环
            outer_boundary = merged_geometry.exterior
        else:
            # 多个多边形，获取所有外环
            outer_boundaries = []
            for geom in merged_geometry.geoms:
                if isinstance(geom, Polygon):
                    outer_boundaries.append(geom.exterior)
            
            # 如果有多个外边界，可能需要进一步处理
            # 这里我们保留所有外边界
            from shapely.geometry import MultiLineString
            if len(outer_boundaries) == 1:
                outer_boundary = outer_boundaries[0]
            else:
                outer_boundary = MultiLineString(outer_boundaries)
    else:
        raise ValueError("输入的几何体类型不支持边界提取")
    
    # 创建新的GeoDataFrame
    boundary_gdf = gpd.GeoDataFrame(
        {'id': [1], 'type': ['outer_boundary']}, 
        geometry=[outer_boundary],
        crs=gdf.crs
    )
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_shp)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存结果
    print(f"正在保存结果到: {output_shp}")
    boundary_gdf.to_file(output_shp)
    
    print("处理完成！")
    print(f"输出文件: {output_shp}")
    print(f"边界几何体类型: {type(outer_boundary).__name__}")

def main():
    """主函数"""
    
    # 输入和输出文件路径
    input_file = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05.shp"
    output_file = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05_boundary.shp"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}")
        return
    
    try:
        # 提取边界
        extract_outer_boundary(input_file, output_file)
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
